globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/dashboard/[call_id]/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/script.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/script.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/tempo-init.tsx":{"*":{"id":"(ssr)/./src/components/tempo-init.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/theme-provider.tsx":{"*":{"id":"(ssr)/./src/components/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@radix-ui/react-label/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/submit-button.tsx":{"*":{"id":"(ssr)/./src/components/submit-button.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/user-profile.tsx":{"*":{"id":"(ssr)/./src/components/user-profile.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/dashboard-navbar.tsx":{"*":{"id":"(ssr)/./src/components/dashboard-navbar.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/call-interface.tsx":{"*":{"id":"(ssr)/./src/components/call-interface.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/Logivance/cortexa/services.ui/node_modules/next/dist/client/script.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/script.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Logivance/cortexa/services.ui/node_modules/next/dist/esm/client/script.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/script.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Logivance/cortexa/services.ui/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Logivance/cortexa/services.ui/src/app/globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Logivance/cortexa/services.ui/src/components/tempo-init.tsx":{"id":"(app-pages-browser)/./src/components/tempo-init.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Logivance/cortexa/services.ui/src/components/theme-provider.tsx":{"id":"(app-pages-browser)/./src/components/theme-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Logivance/cortexa/services.ui/node_modules/next/dist/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Logivance/cortexa/services.ui/node_modules/next/dist/esm/client/components/app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Logivance/cortexa/services.ui/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Logivance/cortexa/services.ui/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Logivance/cortexa/services.ui/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Logivance/cortexa/services.ui/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Logivance/cortexa/services.ui/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Logivance/cortexa/services.ui/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Logivance/cortexa/services.ui/node_modules/next/dist/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Logivance/cortexa/services.ui/node_modules/next/dist/esm/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Logivance/cortexa/services.ui/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Logivance/cortexa/services.ui/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Logivance/cortexa/services.ui/node_modules/@radix-ui/react-label/dist/index.mjs":{"id":"(app-pages-browser)/./node_modules/@radix-ui/react-label/dist/index.mjs","name":"*","chunks":[],"async":false},"/Users/<USER>/Logivance/cortexa/services.ui/node_modules/next/dist/client/link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/dashboard/page","static/chunks/app/dashboard/page.js"],"async":false},"/Users/<USER>/Logivance/cortexa/services.ui/node_modules/next/dist/esm/client/link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/dashboard/page","static/chunks/app/dashboard/page.js"],"async":false},"/Users/<USER>/Logivance/cortexa/services.ui/src/components/submit-button.tsx":{"id":"(app-pages-browser)/./src/components/submit-button.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Logivance/cortexa/services.ui/src/components/user-profile.tsx":{"id":"(app-pages-browser)/./src/components/user-profile.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Logivance/cortexa/services.ui/src/components/dashboard-navbar.tsx":{"id":"(app-pages-browser)/./src/components/dashboard-navbar.tsx","name":"*","chunks":["app/dashboard/[call_id]/page","static/chunks/app/dashboard/%5Bcall_id%5D/page.js"],"async":false},"/Users/<USER>/Logivance/cortexa/services.ui/src/components/call-interface.tsx":{"id":"(app-pages-browser)/./src/components/call-interface.tsx","name":"*","chunks":["app/dashboard/[call_id]/page","static/chunks/app/dashboard/%5Bcall_id%5D/page.js"],"async":false}},"entryCSSFiles":{"/Users/<USER>/Logivance/cortexa/services.ui/src/":[],"/Users/<USER>/Logivance/cortexa/services.ui/src/app/page":[],"/Users/<USER>/Logivance/cortexa/services.ui/src/app/layout":["static/css/app/layout.css"],"/Users/<USER>/Logivance/cortexa/services.ui/src/app/dashboard/page":[],"/Users/<USER>/Logivance/cortexa/services.ui/src/app/dashboard/[call_id]/page":[]}}